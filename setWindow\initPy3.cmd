@echo off
REM ==========================================
REM 一键安装部署Python 3程序（官方默认路径，支持uv）
REM 适用于Windows 11环境
REM ==========================================

REM 1. 设置官方默认路径变量
set PYTHON_DIR=C:\Program Files\Python311
set PYTHON_EXE=%PYTHON_DIR%\python.exe
set PIP_EXE=%PYTHON_DIR%\Scripts\pip.exe
set UV_EXE=%PYTHON_DIR%\Scripts\uv.exe

REM 2. 配置PATH环境变量（仅对当前会话生效）
set PATH=%PYTHON_DIR%;%PYTHON_DIR%\Scripts;%PATH%

REM 3. 检查是否已安装Python 3
echo 检查Python 3是否已安装...
if exist "%PYTHON_EXE%" (
    echo 已检测到Python 3，路径为：%PYTHON_EXE%
) else (
    echo 未检测到Python 3，正在下载安装...
    set PYTHON_INSTALLER=python-3.11.5-amd64.exe
    if not exist %PYTHON_INSTALLER% (
        powershell -Command "Invoke-WebRequest -Uri https://www.python.org/ftp/python/3.11.5/python-3.11.5-amd64.exe -OutFile %PYTHON_INSTALLER%"
    )
    "%~dp0%PYTHON_INSTALLER%" /quiet InstallAllUsers=1 PrependPath=1 Include_pip=1
    echo Python 3安装完成。
)

REM 4. 检查pip是否可用
echo 检查pip是否可用...
"%PIP_EXE%" --version >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    echo pip不可用，正在尝试修复...
    "%PYTHON_EXE%" -m ensurepip
    "%PYTHON_EXE%" -m pip install --upgrade pip
)

REM 5. 检查uv是否已安装
echo 检查uv是否已安装...
"%UV_EXE%" --version >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    echo 未检测到uv，正在安装uv...
    "%PIP_EXE%" install uv
    echo uv安装完成。
) else (
    echo 已检测到uv。
)

REM 6. 安装依赖包（优先使用uv，其次pip）
if exist requirements.txt (
    echo 检测到requirements.txt，优先使用uv安装依赖...
    "%UV_EXE%" pip install -r requirements.txt
    IF %ERRORLEVEL% NEQ 0 (
        echo uv安装依赖失败，尝试使用pip安装...
        "%PIP_EXE%" install -r requirements.txt
    )
) else (
    echo 未检测到requirements.txt，跳过依赖安装。
)

REM 7. 运行你的Python程序（假设主程序为main.py）
if exist main.py (
    echo 正在启动Python程序...
    "%PYTHON_EXE%" main.py
) else (
    echo 未检测到main.py，请检查程序文件。
)

echo 部署完成，按任意键退出...
pause >nul
