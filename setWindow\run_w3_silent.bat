@echo off
REM ============================================================================
REM Window Management Tool Silent Launcher
REM ============================================================================
REM Author: Liu Lifu
REM Version: 2025-01-11-16:15:00
REM Purpose: Launch w3.py without showing command window
REM ============================================================================

REM Change to script directory
cd /d "%~dp0"

REM Check if Python is available and w3.py exists
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python 3.x
    timeout /t 3 >nul
    exit /b 1
)

if not exist "w3.py" (
    echo ERROR: w3.py not found in current directory
    timeout /t 3 >nul
    exit /b 1
)

REM Launch Python script in background and exit immediately
start "" python w3.py
exit
