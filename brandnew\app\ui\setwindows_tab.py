"""
setWindows Tab implementation for window management.
Integrates setWindow project functionality into Brandnew as a tab.
Maintains functional programming style and follows Brandnew patterns.
"""
import sys
from typing import Dict, List, Tuple, Optional

from app.common.utils import append_log

try:
    from PyQt5 import QtWidgets, QtGui, QtCore
    from PyQt5.QtCore import Qt
    from PyQt5.QtWidgets import QDesktopWidget
    import pygetwindow as gw
except Exception as e:  # pragma: no cover
    raise RuntimeError("PyQt5 and pygetwindow are required for setWindows functionality.") from e


# Common aspect ratios for window resizing
COMMON_RATIOS = {
    "": None,  # Empty item
    "16:9": (16, 9),
    "4:3": (4, 3),
    "21:9": (21, 9),
    "1:1": (1, 1),
    "3:2": (3, 2),
    "Custom": None
}

# Common preset resolutions
COMMON_PIXELS = [
    "",  # Empty item
    "1920x1080",   # 16:9, common 27" monitor
    "2560x1440",   # 16:9, high-res 27" monitor
    "3840x2160",   # 16:9, 4K
    "2880x1800",   # 16:10, high-res 13" laptop
    "1920x1200",   # 16:10, common 13" laptop
    "2736x1824",   # 3:2, Surface Book
    "2256x1504",   # 3:2, Surface Laptop
    "1600x900",    # 16:9, low-res 13" laptop
    "1366x768"     # 16:9, basic 13" laptop
]


def get_window_list() -> List[str]:
    """Get list of all visible windows."""
    try:
        windows = gw.getAllWindows()
        # Filter out empty titles and system windows
        visible_windows = [w.title for w in windows if w.title.strip() and w.visible]
        return sorted(list(set(visible_windows)))
    except Exception:
        return []


def get_window_display_title(window_title: str) -> str:
    """Get display title with application type prefix."""
    try:
        windows = gw.getWindowsWithTitle(window_title)
        if windows:
            # Simple classification based on common application patterns
            title_lower = window_title.lower()
            if any(browser in title_lower for browser in ['chrome', 'firefox', 'edge', 'safari']):
                return f"[Browser] {window_title}"
            elif any(office in title_lower for office in ['word', 'excel', 'powerpoint', 'outlook']):
                return f"[Office] {window_title}"
            elif any(dev in title_lower for dev in ['visual studio', 'code', 'pycharm', 'notepad']):
                return f"[Dev] {window_title}"
            else:
                return f"[App] {window_title}"
    except Exception:
        pass
    return window_title


def get_all_monitors() -> List[Dict]:
    """Get information about all monitors."""
    desktop = QDesktopWidget()
    monitors = []
    
    for i in range(desktop.screenCount()):
        screen_rect = desktop.screenGeometry(i)
        available_rect = desktop.availableGeometry(i)
        is_primary = (i == desktop.primaryScreen())
        
        monitor_info = {
            'index': i,
            'geometry': {
                'x': screen_rect.x(),
                'y': screen_rect.y(),
                'width': screen_rect.width(),
                'height': screen_rect.height()
            },
            'available_geometry': {
                'x': available_rect.x(),
                'y': available_rect.y(),
                'width': available_rect.width(),
                'height': available_rect.height()
            },
            'is_primary': is_primary,
            'name': f"Monitor {i + 1}" + (" (Primary)" if is_primary else "")
        }
        monitors.append(monitor_info)
    
    return monitors


def get_screen_info() -> Tuple[int, int]:
    """Get primary screen dimensions."""
    desktop = QDesktopWidget()
    screen_rect = desktop.screenGeometry()
    return screen_rect.width(), screen_rect.height()


def get_screen_center(width: int, height: int, monitor_index: Optional[int] = None) -> Tuple[int, int]:
    """Calculate center position for given window size."""
    if monitor_index is not None:
        monitors = get_all_monitors()
        if monitor_index < len(monitors):
            geometry = monitors[monitor_index]['available_geometry']
            x = geometry['x'] + (geometry['width'] - width) // 2
            y = geometry['y'] + (geometry['height'] - height) // 2
            return x, y
    
    # Default to primary screen
    screen_width, screen_height = get_screen_info()
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    return x, y


def set_window_size(window_title: str, width: int, height: int, center: bool = True, monitor_index: Optional[int] = None) -> Tuple[bool, str]:
    """Set window size and optionally center it."""
    try:
        windows = gw.getWindowsWithTitle(window_title)
        if not windows:
            return False, f"Window '{window_title}' not found"
        
        win = windows[0]
        win.restore()
        win.resizeTo(width, height)
        
        if center:
            x, y = get_screen_center(width, height, monitor_index)
            win.moveTo(x, y)
        
        win.activate()
        monitor_text = f" on Monitor {monitor_index + 1}" if monitor_index is not None else ""
        return True, f"Window resized to {width}x{height} and centered{monitor_text}"
    except Exception as e:
        return False, f"Resize failed: {e}"


def move_window_to_monitor(window_title: str, monitor_index: int) -> Tuple[bool, str]:
    """Move window to specified monitor."""
    try:
        monitors = get_all_monitors()
        if monitor_index >= len(monitors):
            return False, f"Monitor {monitor_index + 1} does not exist"
        
        windows = gw.getWindowsWithTitle(window_title)
        if not windows:
            return False, f"Window '{window_title}' not found"
        
        win = windows[0]
        win.restore()
        
        # Get target monitor info
        target_monitor = monitors[monitor_index]
        target_geometry = target_monitor['available_geometry']
        
        # Get current window size
        current_width = win.width
        current_height = win.height
        
        # Ensure window size doesn't exceed target monitor
        max_width = target_geometry['width']
        max_height = target_geometry['height']
        
        new_width = min(current_width, max_width)
        new_height = min(current_height, max_height)
        
        # Calculate center position on target monitor
        x = target_geometry['x'] + (target_geometry['width'] - new_width) // 2
        y = target_geometry['y'] + (target_geometry['height'] - new_height) // 2
        
        # Resize if necessary and move
        if new_width != current_width or new_height != current_height:
            win.resizeTo(new_width, new_height)
        
        win.moveTo(x, y)
        win.activate()
        
        return True, f"Window moved to {target_monitor['name']}"
    except Exception as e:
        return False, f"Move failed: {e}"


def maximize_window(window_title: str) -> Tuple[bool, str]:
    """Maximize window."""
    try:
        windows = gw.getWindowsWithTitle(window_title)
        if not windows:
            return False, f"Window '{window_title}' not found"
        
        win = windows[0]
        win.maximize()
        win.activate()
        return True, "Window maximized"
    except Exception as e:
        return False, f"Maximize failed: {e}"


def minimize_window(window_title: str) -> Tuple[bool, str]:
    """Minimize window."""
    try:
        windows = gw.getWindowsWithTitle(window_title)
        if not windows:
            return False, f"Window '{window_title}' not found"
        
        win = windows[0]
        win.minimize()
        return True, "Window minimized"
    except Exception as e:
        return False, f"Minimize failed: {e}"


def close_window(window_title: str) -> Tuple[bool, str]:
    """Close window."""
    try:
        windows = gw.getWindowsWithTitle(window_title)
        if not windows:
            return False, f"Window '{window_title}' not found"
        
        win = windows[0]
        win.close()
        return True, "Window closed"
    except Exception as e:
        return False, f"Close failed: {e}"


def calculate_ratio_size(ratio: Tuple[int, int], current_width: int, current_height: int, scale_factor: float = 1.0) -> Tuple[int, int]:
    """Calculate new size based on aspect ratio and scale factor."""
    ratio_w, ratio_h = ratio
    
    # Use current height as base, calculate width
    new_height = int(current_height * scale_factor)
    new_width = int(new_height * ratio_w / ratio_h)
    
    return new_width, new_height


def validate_size_bounds(width: int, height: int) -> Tuple[bool, str]:
    """Validate if size is within reasonable bounds."""
    MIN_SIZE = 300
    screen_width, screen_height = get_screen_info()
    
    # Check minimum size
    if width < MIN_SIZE or height < MIN_SIZE:
        return False, f"Size cannot be smaller than {MIN_SIZE}x{MIN_SIZE}"
    
    # Check maximum size (not exceeding screen)
    if width > screen_width or height > screen_height:
        return False, f"Size cannot exceed screen size {screen_width}x{screen_height}"
    
    return True, "Size is appropriate"


class SetWindowsTab(QtWidgets.QWidget):
    """setWindows functionality integrated as a tab."""
    
    def __init__(self, cfg, log_path, parent=None):
        super().__init__(parent)
        self.cfg = cfg
        self.log_path = log_path
        self.monitors = get_all_monitors()
        self.original_windows = []
        self._setup_ui()
        
    def _setup_ui(self):
        """Setup the user interface."""
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setSpacing(8)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Header
        header_label = QtWidgets.QLabel("Window Management Tool")
        header_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin: 10px 0;")
        main_layout.addWidget(header_label)
        
        # 1. Target Window Selection
        win_group = QtWidgets.QGroupBox("Target Window")
        win_layout = QtWidgets.QVBoxLayout()
        win_layout.setSpacing(5)
        
        # Window selection row
        select_layout = QtWidgets.QHBoxLayout()
        self.window_combo = QtWidgets.QComboBox()
        self.window_combo.setMaximumHeight(24)
        
        refresh_btn = QtWidgets.QPushButton("Refresh")
        refresh_btn.setMaximumSize(60, 24)
        refresh_btn.clicked.connect(self._refresh_window_list)
        
        select_layout.addWidget(self.window_combo, 1)
        select_layout.addWidget(refresh_btn)
        
        # Window control buttons
        control_layout = QtWidgets.QHBoxLayout()
        control_layout.setSpacing(3)
        
        self.maximize_btn = QtWidgets.QPushButton("Maximize")
        self.minimize_btn = QtWidgets.QPushButton("Minimize")
        self.close_btn = QtWidgets.QPushButton("Close")
        
        for btn in [self.maximize_btn, self.minimize_btn, self.close_btn]:
            btn.setMaximumSize(70, 24)
        
        self.maximize_btn.clicked.connect(self._on_maximize_clicked)
        self.minimize_btn.clicked.connect(self._on_minimize_clicked)
        self.close_btn.clicked.connect(self._on_close_clicked)
        
        self.close_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-size: 11px; }")
        self.maximize_btn.setStyleSheet("QPushButton { font-size: 11px; }")
        self.minimize_btn.setStyleSheet("QPushButton { font-size: 11px; }")
        
        control_layout.addWidget(self.maximize_btn)
        control_layout.addWidget(self.minimize_btn)
        control_layout.addWidget(self.close_btn)
        
        win_layout.addLayout(select_layout)
        win_layout.addLayout(control_layout)
        win_group.setLayout(win_layout)
        main_layout.addWidget(win_group)
        
        # 2. Size Settings
        size_group = QtWidgets.QGroupBox("Size Settings")
        size_layout = QtWidgets.QVBoxLayout()
        
        # Preset resolution
        preset_layout = QtWidgets.QHBoxLayout()
        preset_layout.addWidget(QtWidgets.QLabel("Preset:"))
        self.pixel_combo = QtWidgets.QComboBox()
        self.pixel_combo.addItems(COMMON_PIXELS)
        self.pixel_combo.currentTextChanged.connect(self._on_preset_changed)
        preset_layout.addWidget(self.pixel_combo, 1)
        size_layout.addLayout(preset_layout)
        
        # Custom size
        custom_layout = QtWidgets.QHBoxLayout()
        custom_layout.addWidget(QtWidgets.QLabel("Custom:"))
        self.width_input = QtWidgets.QLineEdit()
        self.width_input.setPlaceholderText("Width")
        self.width_input.setMaximumWidth(80)
        custom_layout.addWidget(self.width_input)
        custom_layout.addWidget(QtWidgets.QLabel("×"))
        self.height_input = QtWidgets.QLineEdit()
        self.height_input.setPlaceholderText("Height")
        self.height_input.setMaximumWidth(80)
        custom_layout.addWidget(self.height_input)
        custom_layout.addStretch()
        size_layout.addLayout(custom_layout)
        
        # Aspect ratio with enlarge/shrink
        ratio_layout = QtWidgets.QHBoxLayout()
        ratio_layout.addWidget(QtWidgets.QLabel("Ratio:"))
        self.ratio_combo = QtWidgets.QComboBox()
        self.ratio_combo.addItems(list(COMMON_RATIOS.keys()))
        self.ratio_combo.currentTextChanged.connect(self._update_ratio_buttons_state)
        ratio_layout.addWidget(self.ratio_combo, 1)
        
        self.enlarge_btn = QtWidgets.QPushButton("+")
        self.shrink_btn = QtWidgets.QPushButton("-")
        for btn in [self.enlarge_btn, self.shrink_btn]:
            btn.setMaximumSize(30, 24)
            btn.setEnabled(False)
        
        self.enlarge_btn.clicked.connect(self._on_enlarge_clicked)
        self.shrink_btn.clicked.connect(self._on_shrink_clicked)
        
        ratio_layout.addWidget(self.enlarge_btn)
        ratio_layout.addWidget(self.shrink_btn)
        size_layout.addLayout(ratio_layout)
        
        size_group.setLayout(size_layout)
        main_layout.addWidget(size_group)
        
        # 3. Monitor Selection
        monitor_group = QtWidgets.QGroupBox("Monitor Selection")
        monitor_layout = QtWidgets.QHBoxLayout()
        
        self.monitor_buttons = []
        for i, monitor in enumerate(self.monitors):
            btn = QtWidgets.QPushButton(f"Monitor {i + 1}")
            btn.setMaximumSize(80, 30)
            btn.clicked.connect(lambda checked, idx=i: self._on_monitor_button_clicked(idx))
            self.monitor_buttons.append(btn)
            monitor_layout.addWidget(btn)
        
        if not self.monitor_buttons:
            monitor_layout.addWidget(QtWidgets.QLabel("No monitors detected"))
        
        monitor_group.setLayout(monitor_layout)
        main_layout.addWidget(monitor_group)
        
        # 4. Action Buttons
        action_layout = QtWidgets.QHBoxLayout()
        
        self.set_btn = QtWidgets.QPushButton("Adjust Size")
        self.set_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.set_btn.clicked.connect(self._on_set_clicked)
        
        reset_btn = QtWidgets.QPushButton("Reset")
        reset_btn.clicked.connect(self._on_reset_clicked)
        
        action_layout.addWidget(self.set_btn, 1)
        action_layout.addWidget(reset_btn)
        main_layout.addLayout(action_layout)
        
        # 5. Status Bar
        self.status_label = QtWidgets.QLabel("Ready")
        self.status_label.setMinimumHeight(24)
        self.status_label.setMaximumHeight(24)
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
                color: #2c3e50;
            }
        """)
        main_layout.addWidget(self.status_label)
        
        # Initialize and refresh
        self._on_reset_clicked()
        self._refresh_window_list()
        self._update_monitor_buttons()
        
    def _show_message(self, message: str, msg_type: str = "info"):
        """Show status message with appropriate styling."""
        colors = {
            "info": "#3498db",
            "success": "#27ae60", 
            "warning": "#f39c12",
            "error": "#e74c3c"
        }
        color = colors.get(msg_type, "#3498db")
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                color: white;
                border: 1px solid {color};
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
            }}
        """)
        
        # Reset to default style after 3 seconds
        QtCore.QTimer.singleShot(3000, lambda: self.status_label.setStyleSheet("""
            QLabel {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                padding: 4px 8px;
                border-radius: 3px;
                font-size: 11px;
                color: #2c3e50;
            }
        """))
        
    def _refresh_window_list(self):
        """Refresh the window list."""
        self.window_combo.clear()
        windows = get_window_list()
        display_windows = [get_window_display_title(w) for w in windows]
        self.window_combo.addItems(display_windows)
        self.original_windows = windows
        
        self._update_monitor_buttons()
        monitor_count = len(self.monitors)
        self._show_message(f"Refreshed: found {len(windows)} windows, {monitor_count} monitors", "info")
        
    def _update_monitor_buttons(self):
        """Update monitor button states."""
        for i, btn in enumerate(self.monitor_buttons):
            if i < len(self.monitors):
                monitor = self.monitors[i]
                btn.setText(monitor['name'])
                btn.setEnabled(True)
                if monitor['is_primary']:
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #27ae60;
                            color: white;
                            font-size: 11px;
                            border: 2px solid #229954;
                            border-radius: 4px;
                        }
                    """)
                else:
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #3498db;
                            color: white;
                            font-size: 11px;
                            border: 2px solid #2980b9;
                            border-radius: 4px;
                        }
                    """)
            else:
                btn.setText(f"Monitor {i + 1}")
                btn.setEnabled(False)
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: #bdc3c7;
                        color: #7f8c8d;
                        font-size: 11px;
                        border: 2px solid #95a5a6;
                        border-radius: 4px;
                    }
                """)
                
    def _update_ratio_buttons_state(self):
        """Update enlarge/shrink button states based on ratio selection."""
        ratio_key = self.ratio_combo.currentText()
        has_ratio = ratio_key and ratio_key not in ["", "Custom"] and ratio_key in COMMON_RATIOS
        self.enlarge_btn.setEnabled(has_ratio)
        self.shrink_btn.setEnabled(has_ratio)
        
    def _on_preset_changed(self, text: str):
        """Handle preset resolution selection."""
        if text and "x" in text:
            try:
                width, height = map(int, text.split("x"))
                self.width_input.setText(str(width))
                self.height_input.setText(str(height))
            except ValueError:
                pass
                
    def _get_selected_window_title(self) -> str:
        """Get the original window title for the selected window."""
        current_index = self.window_combo.currentIndex()
        if 0 <= current_index < len(self.original_windows):
            return self.original_windows[current_index]
        return ""
        
    def _get_current_size_from_inputs(self) -> Tuple[int, int]:
        """Get current size from input fields."""
        try:
            width = int(self.width_input.text()) if self.width_input.text() else 800
            height = int(self.height_input.text()) if self.height_input.text() else 600
            return width, height
        except ValueError:
            return 800, 600

    def _on_monitor_button_clicked(self, monitor_index: int):
        """Handle monitor button click."""
        window_title = self._get_selected_window_title()
        if not window_title:
            self._show_message("Please select a window first", "warning")
            return

        if monitor_index >= len(self.monitors):
            self._show_message(f"Monitor {monitor_index + 1} is not available", "error")
            return

        # Move window to specified monitor
        ok, msg = move_window_to_monitor(window_title, monitor_index)
        if ok:
            self._show_message(msg, "success")
            # Log the action
            append_log(self.log_path, action="move_window", group="SetWindows",
                      detail=f"{window_title} -> Monitor {monitor_index + 1}")
        else:
            self._show_message(msg, "error")

    def _on_maximize_clicked(self):
        """Handle maximize button click."""
        window_title = self._get_selected_window_title()
        if not window_title:
            self._show_message("Please select a window first", "warning")
            return

        ok, msg = maximize_window(window_title)
        if ok:
            self._show_message(msg, "success")
            append_log(self.log_path, action="maximize", group="SetWindows", detail=window_title)
        else:
            self._show_message(msg, "error")

    def _on_minimize_clicked(self):
        """Handle minimize button click."""
        window_title = self._get_selected_window_title()
        if not window_title:
            self._show_message("Please select a window first", "warning")
            return

        ok, msg = minimize_window(window_title)
        if ok:
            self._show_message(msg, "success")
            append_log(self.log_path, action="minimize", group="SetWindows", detail=window_title)
        else:
            self._show_message(msg, "error")

    def _on_close_clicked(self):
        """Handle close button click with confirmation."""
        window_title = self._get_selected_window_title()
        if not window_title:
            self._show_message("Please select a window first", "warning")
            return

        # Confirmation dialog
        reply = QtWidgets.QMessageBox.question(
            self,
            "Confirm Close",
            f"Are you sure you want to close the window '{window_title}'?",
            QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No
        )

        if reply == QtWidgets.QMessageBox.StandardButton.Yes:
            ok, msg = close_window(window_title)
            if ok:
                self._show_message(msg, "success")
                append_log(self.log_path, action="close", group="SetWindows", detail=window_title)
                # Refresh window list since window was closed
                self._refresh_window_list()
            else:
                self._show_message(msg, "error")

    def _on_enlarge_clicked(self):
        """Handle enlarge button click."""
        ratio_key = self.ratio_combo.currentText()
        if not ratio_key or ratio_key in ["", "Custom"]:
            self._show_message("Please select an aspect ratio first", "warning")
            return

        ratio = COMMON_RATIOS.get(ratio_key)
        if not ratio:
            self._show_message("Invalid ratio setting", "error")
            return

        # Get current size
        current_w, current_h = self._get_current_size_from_inputs()

        # Calculate new size (enlarge by 20%)
        new_w, new_h = calculate_ratio_size(ratio, current_w, current_h, 1.2)

        # Validate size bounds
        valid, msg = validate_size_bounds(new_w, new_h)
        if not valid:
            self._show_message(f"Cannot enlarge: {msg}", "warning")
            return

        # Update input fields
        self.width_input.setText(str(new_w))
        self.height_input.setText(str(new_h))

        self._show_message(f"Enlarged proportionally to {new_w}x{new_h}", "success")

    def _on_shrink_clicked(self):
        """Handle shrink button click."""
        ratio_key = self.ratio_combo.currentText()
        if not ratio_key or ratio_key in ["", "Custom"]:
            self._show_message("Please select an aspect ratio first", "warning")
            return

        ratio = COMMON_RATIOS.get(ratio_key)
        if not ratio:
            self._show_message("Invalid ratio setting", "error")
            return

        # Get current size
        current_w, current_h = self._get_current_size_from_inputs()

        # Calculate new size (shrink by 20%)
        new_w, new_h = calculate_ratio_size(ratio, current_w, current_h, 0.8)

        # Validate size bounds
        valid, msg = validate_size_bounds(new_w, new_h)
        if not valid:
            self._show_message(f"Cannot shrink: {msg}", "warning")
            return

        # Update input fields
        self.width_input.setText(str(new_w))
        self.height_input.setText(str(new_h))

        self._show_message(f"Shrunk proportionally to {new_w}x{new_h}", "success")

    def _on_set_clicked(self):
        """Handle adjust size button click with priority logic."""
        window_title = self._get_selected_window_title()
        if not window_title:
            self._show_message("Please select a window first", "warning")
            return

        w, h = None, None
        method_used = ""

        # Priority 1: Aspect ratio adjustment
        ratio_key = self.ratio_combo.currentText()
        if ratio_key and ratio_key not in ["", "Custom"] and ratio_key in COMMON_RATIOS:
            ratio = COMMON_RATIOS[ratio_key]
            if ratio:
                try:
                    # Use height input if available, otherwise use width, otherwise default to 600
                    if self.height_input.text():
                        h = int(self.height_input.text())
                    elif self.width_input.text():
                        w_temp = int(self.width_input.text())
                        h = int(w_temp * ratio[1] / ratio[0])
                    else:
                        h = 600  # Default height

                    w = int(h * ratio[0] / ratio[1])
                    method_used = f"by ratio {ratio_key}"
                except Exception:
                    self._show_message("Ratio calculation failed, please check input dimensions", "error")
                    return

        # Priority 2: Preset resolution
        elif self.pixel_combo.currentText() and "x" in self.pixel_combo.currentText():
            try:
                w, h = map(int, self.pixel_combo.currentText().split("x"))
                method_used = f"preset {w}x{h}"
            except ValueError:
                self._show_message("Invalid preset resolution format", "error")
                return

        # Priority 3: Custom input
        else:
            try:
                w = int(self.width_input.text()) if self.width_input.text() else None
                h = int(self.height_input.text()) if self.height_input.text() else None
                if w is None or h is None:
                    self._show_message("Please enter both width and height, or select a ratio/preset", "warning")
                    return
                method_used = f"custom {w}x{h}"
            except ValueError:
                self._show_message("Please enter valid numeric dimensions", "error")
                return

        # Validate size bounds
        size_ok, size_msg = validate_size_bounds(w, h)
        if not size_ok:
            self._show_message(f"Size validation failed: {size_msg}", "error")
            return

        # Execute window resize
        ok, msg = set_window_size(window_title, w, h, center=True)
        if ok:
            final_msg = f"{method_used}: {msg}"
            self._show_message(final_msg, "success")
            append_log(self.log_path, action="resize", group="SetWindows",
                      detail=f"{window_title} -> {w}x{h}", extra={"method": method_used})
        else:
            self._show_message(f"Resize failed: {msg}", "error")

    def _on_reset_clicked(self):
        """Reset all input fields to default state."""
        self.pixel_combo.setCurrentIndex(0)
        self.width_input.clear()
        self.height_input.clear()
        self.ratio_combo.setCurrentIndex(0)
        self._update_ratio_buttons_state()
        self._show_message("Reset to default state", "info")
