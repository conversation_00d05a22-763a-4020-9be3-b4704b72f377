# Window Management Tool (w3.py)

**Author:** Liu Lifu  
**Version:** 2025-01-11-15:30:00

## 📋 Overview

A comprehensive Windows application management tool that provides precise window resizing, aspect ratio control, and window state management with an intuitive GUI interface.

## 🚀 Quick Start

### Method 1: Using Batch Files (Recommended)
- **Full launcher:** Double-click `run_w3.bat` for detailed startup information
- **Simple launcher:** Double-click `w3_simple.bat` for quick startup

### Method 2: Direct Python Execution
```bash
cd setWindow
python w3.py
```

## ✨ Key Features

### 🎯 Priority-Based Resizing
1. **Aspect Ratio (Highest Priority)** - 16:9, 4:3, 21:9, 1:1, 3:2
2. **Preset Resolutions** - 1920x1080, 2560x1440, 4K, etc.
3. **Custom Dimensions** - Manual width/height input

### 🔧 Window Controls
- **Maximize/Minimize/Close** - Standard window state controls
- **Enlarge/Shrink** - Proportional scaling (20% increments)
- **Auto-Center** - Windows automatically center after resizing

### 🛡️ Smart Validation
- **Size Limits** - Minimum 300x300, maximum screen resolution
- **Screen Boundary Check** - Warns when dimensions exceed display
- **Ratio Preservation** - Maintains aspect ratios during scaling

### 📱 User Interface
- **Compact Design** - 380x480 pixel window
- **Status Bar** - In-app messages (no popup interruptions)
- **Smart Controls** - Enlarge/Shrink buttons auto-enable with ratio selection
- **Color-Coded Feedback** - Success (green), warning (orange), error (red)

## 🎛️ Interface Layout

```
┌─ Target Window ─────────────────────────┐
│ [Window Dropdown] [Refresh]             │
│ [Maximize] [Minimize] [Close]           │
└─────────────────────────────────────────┘

┌─ Size Settings ─────────────────────────┐
│ Preset: [Resolution Dropdown]           │
│ Custom: [Width] × [Height]              │
│ Ratio:  [Ratio Dropdown] [+] [-]        │
└─────────────────────────────────────────┘

┌─ Actions ───────────────────────────────┐
│ [Adjust Size] [Reset]                   │
└─────────────────────────────────────────┘

┌─ Status ────────────────────────────────┐
│ Ready / Success / Warning / Error       │
└─────────────────────────────────────────┘
```

## 🔄 Usage Workflow

### Basic Window Resizing
1. Select target window from dropdown
2. Choose resize method:
   - **For aspect ratio:** Select ratio → optionally adjust with +/- buttons
   - **For preset size:** Choose from common resolutions
   - **For custom size:** Enter width and height values
3. Click "Adjust Size"
4. Monitor status bar for feedback

### Proportional Scaling
1. Select target window
2. Choose aspect ratio (16:9, 4:3, etc.)
3. Use **Enlarge** (+) or **Shrink** (-) buttons
4. Buttons automatically enable/disable based on ratio selection
5. Size constraints prevent invalid dimensions

### Window State Control
- **Maximize:** Expand window to full screen
- **Minimize:** Reduce window to taskbar
- **Close:** Safely close window (with confirmation)

## ⚙️ System Requirements

- **OS:** Windows 10/11
- **Python:** 3.7+ with PATH configuration
- **Dependencies:**
  ```bash
  pip install PyQt5 pygetwindow
  ```

## 🎨 Technical Features

### Functional Programming Style
- Pure functions for calculations
- Immutable data structures
- Clear separation of concerns

### Smart Priority Logic
```python
# Priority order implementation
if ratio_selected:
    calculate_by_ratio()
elif preset_selected:
    use_preset_resolution()
else:
    use_custom_input()
```

### Boundary Validation
```python
def validate_size_bounds(width, height):
    MIN_SIZE = 300
    screen_w, screen_h = get_screen_info()
    # Validation logic...
```

## 🔍 Troubleshooting

### Common Issues
- **Python not found:** Ensure Python is in system PATH
- **Module errors:** Install required packages with pip
- **GUI not appearing:** Check display drivers and PyQt5 installation
- **Window not found:** Refresh window list or check window visibility

### Error Messages
- **Blue (Info):** General information and status updates
- **Green (Success):** Successful operations
- **Orange (Warning):** Size constraints or validation warnings
- **Red (Error):** Operation failures or system errors

## 📁 File Structure

```
setWindow/
├── w3.py              # Main application
├── run_w3.bat         # Detailed launcher with diagnostics
├── w3_simple.bat      # Simple launcher
└── README.md          # This documentation
```

## 🔄 Version History

- **2025-01-11:** Added enlarge/shrink buttons, improved UI compactness
- **Previous:** Priority-based resizing, status bar, window controls

## 📞 Support

For issues or feature requests, refer to the comprehensive error handling and status messages built into the application.
