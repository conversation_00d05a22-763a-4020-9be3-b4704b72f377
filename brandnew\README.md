## IT Help Desk Access Tool (PyQt)

A modular, extensible Python PyQt GUI application to streamline the IT Help Desk Service workflow for managing company content app access permissions.

- Python 3 + PyQt5
- Functional style: shared functions live in `app/common/utils.py`
- All configurable items are stored in `config/config.ini`
- Logs are stored in `logs/app.log`
- Statistics script in `tools/stats.py`

### Quick start

1. Ensure Python 3.8+ is installed. On Windows, Outlook desktop is required for send-mail features.
2. (Optional) Install dependencies:
   - `pip install -r requirements.txt`
3. Run the app:
   - `python main.py`
4. Open configuration from the "Set" menu.

### Structure

- `main.py` – Application entry point.
- `app/`
  - `common/utils.py` – Pure functions: config I/O, logging setup, validation, OS/browser helpers.
  - `ui/main_window.py` – Main window (tabs, menus, email operations, clock).
  - `ui/config_window.py` – Configuration window (tabbed; websites/background/shortcut).
  - `data/` – Default resources (icons, sample backgrounds).
- `config/config.ini` – All configurable items.
- `logs/app.log` – Runtime copy/send/sendmail logs.
- `tools/stats.py` – Log statistics and visualization.

### Notes
- If matplotlib is missing, `tools/stats.py` falls back to text output and simple ASCII chart.
- Only actions in the "User" group are included in statistics.
- All UI fonts are regular (not bold) except the input box text.

