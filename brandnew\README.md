## IT Help Desk Access Tool (PyQt)

A modular, extensible Python PyQt GUI application to streamline the IT Help Desk Service workflow for managing company content app access permissions, custom URL management, and window management.

- Python 3 + PyQt5
- Functional style: shared functions live in `app/common/utils.py`
- All configurable items are stored in `config/config.ini`
- Custom links stored in `config/links.json`
- Logs are stored in `logs/app.log`
- Statistics script in `tools/stats.py`
- Integrated window management functionality

### Quick start

1. Ensure Python 3.8+ is installed. On Windows, Outlook desktop is required for send-mail features.
2. Install dependencies:
   - `pip install -r requirements.txt`
3. Run the app:
   - `python main.py`
4. Open configuration from the "Set" menu.

### Features

#### Betelligent Tab
- IT Help Desk workflow for managing company app access permissions
- Email template automation with Outlook integration
- User management and identification
- Teams integration

#### Links Tab
- Custom URL management with add/edit/delete functionality
- Support for long URLs (e.g., SharePoint directory links)
- URL preview and copy functionality
- Organized table view with action buttons

#### setWindows Tab
- Window resizing with aspect ratio control (16:9, 4:3, 21:9, etc.)
- Preset resolution options (1920x1080, 2560x1440, 4K, etc.)
- Custom dimension input
- Multi-monitor support
- Window state management (maximize/minimize/close)
- Proportional scaling with enlarge/shrink buttons

### Structure

- `main.py` – Application entry point.
- `app/`
  - `common/utils.py` – Pure functions: config I/O, logging setup, validation, OS/browser helpers.
  - `ui/main_window.py` – Main window (tabs, menus, email operations, clock).
  - `ui/config_window.py` – Configuration window (tabbed; background/shortcut/teams).
  - `ui/links_tab.py` – Links management tab (URL add/edit/delete/copy functionality).
  - `ui/setwindows_tab.py` – Window management tab (integrated setWindow functionality).
  - `data/` – Default resources (icons, sample backgrounds).
- `config/config.ini` – All configurable items.
- `config/links.json` – Custom URL links data.
- `logs/app.log` – Runtime copy/send/sendmail/links/window management logs.
- `tools/stats.py` – Log statistics and visualization.
- `setWindow/` – Independent window management tool (maintained separately).

### Notes
- If matplotlib is missing, `tools/stats.py` falls back to text output and simple ASCII chart.
- Only actions in the "User" group are included in statistics.
- All UI fonts are regular (not bold) except the input box text.
- Links functionality replaces the previous Goto menu system.
- setWindows tab integrates window management while maintaining setWindow as independent tool.
- pygetwindow dependency required for window management features.

