"""
Integration test script for the enhanced Brandnew application.
Tests the new Links and setWindows tabs functionality.
"""
import sys
import os
import json
from unittest.mock import patch

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_links_functionality():
    """Test Links tab functionality."""
    print("Testing Links functionality...")
    
    try:
        from app.ui.links_tab import load_links_data, save_links_data, validate_url, truncate_url
        
        # Test URL validation
        assert validate_url("https://example.com") == True
        assert validate_url("http://test.org") == True
        assert validate_url("invalid-url") == False
        assert validate_url("") == False
        print("✓ URL validation works correctly")
        
        # Test URL truncation
        long_url = "https://beigeneo365.sharepoint.com/:p:/s/ITRT/Ef5hXvL5bvJCnKROR28AY3sBF_xCF9Pn5qzr8_kr1kkAWw?e=zDoAo6&CID=94880355-AAF6-43E9-9390-E0A95392293E"
        truncated = truncate_url(long_url, 50)
        assert len(truncated) <= 50
        assert truncated.endswith("...")
        print("✓ URL truncation works correctly")
        
        # Test data persistence
        test_config_path = "test_config.ini"
        test_links = [
            {"name": "Test Link 1", "url": "https://example.com"},
            {"name": "Test Link 2", "url": "https://test.org"}
        ]
        
        # Save and load test
        assert save_links_data(test_config_path, test_links) == True
        loaded_links = load_links_data(test_config_path)
        assert len(loaded_links) == 2
        assert loaded_links[0]["name"] == "Test Link 1"
        
        # Cleanup
        links_file = os.path.join(os.path.dirname(test_config_path), "links.json")
        if os.path.exists(links_file):
            os.remove(links_file)
        
        print("✓ Links data persistence works correctly")
        
    except Exception as e:
        print(f"✗ Links functionality test failed: {e}")
        return False
    
    return True


def test_setwindows_functionality():
    """Test setWindows tab functionality."""
    print("Testing setWindows functionality...")

    try:
        # Mock QApplication for testing
        with patch('PyQt5.QtWidgets.QApplication'):
            with patch('PyQt5.QtWidgets.QDesktopWidget'):
                from app.ui.setwindows_tab import (
                    get_window_list, validate_size_bounds,
                    calculate_ratio_size, COMMON_RATIOS
                )

                # Test window list (may be empty in test environment)
                windows = get_window_list()
                assert isinstance(windows, list)
                print(f"✓ Window list detection works: found {len(windows)} window(s)")

                # Test size validation
                assert validate_size_bounds(800, 600)[0] == True
                assert validate_size_bounds(100, 100)[0] == False  # Too small
                print("✓ Size validation works correctly")

                # Test ratio calculations
                ratio = COMMON_RATIOS["16:9"]
                new_w, new_h = calculate_ratio_size(ratio, 800, 600, 1.2)
                assert isinstance(new_w, int) and isinstance(new_h, int)
                print("✓ Ratio calculations work correctly")

    except Exception as e:
        print(f"✗ setWindows functionality test failed: {e}")
        return False

    return True


def test_main_window_integration():
    """Test main window integration with new tabs."""
    print("Testing main window integration...")
    
    try:
        # Mock QApplication to avoid GUI in test
        with patch('app.ui.main_window.QtWidgets.QApplication'):
            from app.ui.main_window import MainWindow
            from app.common.utils import load_config, setup_logging
            
            cfg = load_config()
            log_path = setup_logging(cfg)
            
            # Test MainWindow creation (without showing)
            with patch('app.ui.main_window.QtWidgets.QMainWindow.__init__'):
                with patch('app.ui.main_window.QtWidgets.QTabWidget'):
                    window = MainWindow.__new__(MainWindow)
                    window.cfg = cfg
                    window.log_path = log_path
                    
                    # Test tab creation methods exist
                    assert hasattr(MainWindow, '_build_links_tab')
                    assert hasattr(MainWindow, '_build_setwindows_tab')
                    assert hasattr(MainWindow, '_build_email_tab')
                    
        print("✓ Main window integration works correctly")
        
    except Exception as e:
        print(f"✗ Main window integration test failed: {e}")
        return False
    
    return True


def test_config_structure():
    """Test configuration file structure."""
    print("Testing configuration structure...")
    
    try:
        from app.common.utils import load_config
        
        cfg = load_config()
        
        # Check required sections exist
        required_sections = ['app', 'logging', 'user', 'links', 'sendmail', 'background', 'teams']
        for section in required_sections:
            if not cfg.has_section(section):
                print(f"✗ Missing required section: {section}")
                return False
        
        # Check links.json exists
        links_file = os.path.join("config", "links.json")
        if not os.path.exists(links_file):
            print(f"✗ Missing links.json file")
            return False
        
        # Validate links.json format
        with open(links_file, 'r', encoding='utf-8') as f:
            links_data = json.load(f)
            assert isinstance(links_data, list)
            if links_data:
                assert 'name' in links_data[0]
                assert 'url' in links_data[0]
        
        print("✓ Configuration structure is correct")
        
    except Exception as e:
        print(f"✗ Configuration structure test failed: {e}")
        return False
    
    return True


def main():
    """Run all integration tests."""
    print("=" * 60)
    print("Brandnew Enhanced Application Integration Tests")
    print("=" * 60)
    
    tests = [
        test_config_structure,
        test_links_functionality,
        test_setwindows_functionality,
        test_main_window_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The integration is successful.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
