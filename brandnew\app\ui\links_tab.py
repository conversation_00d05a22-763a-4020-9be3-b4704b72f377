"""
Links Tab implementation for URL management.
Provides add/edit/delete/copy functionality for custom URL shortcuts.
Handles long URLs with preview functionality.
"""
import os
import json
from typing import Dict, List, Optional
from urllib.parse import urlparse

from app.common.utils import (
    load_config,
    open_in_edge,
    resource_path,
    append_log
)

try:
    from PyQt5 import QtWidgets, QtGui, QtCore
    from PyQt5.QtCore import Qt
except Exception as e:  # pragma: no cover
    raise RuntimeError("PyQt5 is required to run this application.") from e


def load_links_data(config_path: str) -> List[Dict]:
    """Load links data from JSON file."""
    links_file = os.path.join(os.path.dirname(config_path), "links.json")
    if os.path.exists(links_file):
        try:
            with open(links_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return []
    return []


def save_links_data(config_path: str, links: List[Dict]) -> bool:
    """Save links data to JSON file."""
    links_file = os.path.join(os.path.dirname(config_path), "links.json")
    try:
        with open(links_file, 'w', encoding='utf-8') as f:
            json.dump(links, f, indent=2, ensure_ascii=False)
        return True
    except Exception:
        return False


def validate_url(url: str) -> bool:
    """Validate if the provided string is a valid URL."""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def truncate_url(url: str, max_length: int = 50) -> str:
    """Truncate URL for display purposes."""
    if len(url) <= max_length:
        return url
    return url[:max_length-3] + "..."


class LinkEditDialog(QtWidgets.QDialog):
    """Dialog for adding/editing link entries."""
    
    def __init__(self, parent=None, link_data: Optional[Dict] = None):
        super().__init__(parent)
        self.link_data = link_data or {}
        self.setWindowTitle("Edit Link" if link_data else "Add Link")
        self.setModal(True)
        self.resize(500, 200)
        self._setup_ui()
        
    def _setup_ui(self):
        layout = QtWidgets.QVBoxLayout(self)
        
        # Form layout
        form = QtWidgets.QFormLayout()
        
        # Name field
        self.name_edit = QtWidgets.QLineEdit()
        self.name_edit.setText(self.link_data.get('name', ''))
        self.name_edit.setPlaceholderText("Enter link name (e.g., 'JIRA Dashboard')")
        form.addRow("Name:", self.name_edit)
        
        # URL field
        self.url_edit = QtWidgets.QLineEdit()
        self.url_edit.setText(self.link_data.get('url', ''))
        self.url_edit.setPlaceholderText("Enter full URL (e.g., 'https://example.com')")
        form.addRow("URL:", self.url_edit)
        
        layout.addLayout(form)
        
        # Buttons
        button_layout = QtWidgets.QHBoxLayout()
        
        self.save_btn = QtWidgets.QPushButton("Save")
        self.save_btn.clicked.connect(self.accept)
        
        cancel_btn = QtWidgets.QPushButton("Cancel")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
        # Set focus to name field
        self.name_edit.setFocus()
        
    def get_link_data(self) -> Dict:
        """Get the link data from the dialog."""
        return {
            'name': self.name_edit.text().strip(),
            'url': self.url_edit.text().strip()
        }
        
    def validate_input(self) -> bool:
        """Validate the input data."""
        data = self.get_link_data()
        if not data['name']:
            QtWidgets.QMessageBox.warning(self, "Validation Error", "Please enter a name for the link.")
            return False
        if not data['url']:
            QtWidgets.QMessageBox.warning(self, "Validation Error", "Please enter a URL.")
            return False
        if not validate_url(data['url']):
            QtWidgets.QMessageBox.warning(self, "Validation Error", "Please enter a valid URL (must include http:// or https://).")
            return False
        return True
        
    def accept(self):
        """Override accept to validate input."""
        if self.validate_input():
            super().accept()


class LinksTab(QtWidgets.QWidget):
    """Links management tab widget."""
    
    def __init__(self, cfg, log_path, parent=None):
        super().__init__(parent)
        self.cfg = cfg
        self.log_path = log_path
        self.links_data = []
        self._setup_ui()
        self._load_links()
        
    def _setup_ui(self):
        """Setup the user interface."""
        layout = QtWidgets.QVBoxLayout(self)
        
        # Header
        header_label = QtWidgets.QLabel("Custom URL Links Management")
        header_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin: 10px 0;")
        layout.addWidget(header_label)
        
        # Table for links
        self.table = QtWidgets.QTableWidget(0, 3)
        self.table.setHorizontalHeaderLabels(["Name", "URL", "Preview"])

        # Set column widths
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QtWidgets.QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QtWidgets.QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QtWidgets.QHeaderView.ResizeMode.ResizeToContents)

        # Table styling - compact rows
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setEditTriggers(QtWidgets.QAbstractItemView.EditTrigger.NoEditTriggers)

        # Enable double-click to open URLs
        self.table.itemDoubleClicked.connect(self._on_item_double_clicked)

        # Set compact row height
        self.table.verticalHeader().setDefaultSectionSize(32)
        self.table.verticalHeader().setMinimumSectionSize(28)
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                selection-background-color: #e3f2fd;
            }
            QTableWidget::item {
                padding: 4px;
                border: none;
            }
        """)
        
        layout.addWidget(self.table)

        # Public action buttons
        button_layout = QtWidgets.QHBoxLayout()

        self.copy_btn = QtWidgets.QPushButton("Copy URL")
        self.copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border-radius: 5px;
                padding: 6px 12px;
                font-size: 11px;
            }
            QPushButton:hover { background-color: #e67e22; }
            QPushButton:disabled { background-color: #bdc3c7; color: #7f8c8d; }
        """)
        self.copy_btn.clicked.connect(self._copy_selected_url)
        self.copy_btn.setEnabled(False)

        self.edit_btn = QtWidgets.QPushButton("Edit")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border-radius: 5px;
                padding: 6px 12px;
                font-size: 11px;
            }
            QPushButton:hover { background-color: #8e44ad; }
            QPushButton:disabled { background-color: #bdc3c7; color: #7f8c8d; }
        """)
        self.edit_btn.clicked.connect(self._edit_selected_link)
        self.edit_btn.setEnabled(False)

        self.delete_btn = QtWidgets.QPushButton("Delete")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                padding: 6px 12px;
                font-size: 11px;
            }
            QPushButton:hover { background-color: #c0392b; }
            QPushButton:disabled { background-color: #bdc3c7; color: #7f8c8d; }
        """)
        self.delete_btn.clicked.connect(self._delete_selected_link)
        self.delete_btn.setEnabled(False)

        self.add_btn = QtWidgets.QPushButton("Add New Link")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                padding: 6px 12px;
                font-size: 11px;
            }
            QPushButton:hover { background-color: #229954; }
        """)
        self.add_btn.clicked.connect(self._add_link)

        button_layout.addWidget(self.copy_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.add_btn)

        layout.addLayout(button_layout)

        # Connect selection change to update button states
        self.table.itemSelectionChanged.connect(self._on_selection_changed)
        
    def _load_links(self):
        """Load links from configuration and populate table."""
        from app.common.utils import DEFAULT_CONFIG_PATH
        self.links_data = load_links_data(DEFAULT_CONFIG_PATH)
        self._refresh_table()
        
    def _refresh_table(self):
        """Refresh the table with current links data."""
        self.table.setRowCount(len(self.links_data))

        for row, link in enumerate(self.links_data):
            # Name column
            name_item = QtWidgets.QTableWidgetItem(link.get('name', ''))
            self.table.setItem(row, 0, name_item)

            # URL column (truncated)
            url = link.get('url', '')
            url_item = QtWidgets.QTableWidgetItem(truncate_url(url, 80))
            url_item.setToolTip(url)  # Full URL in tooltip
            self.table.setItem(row, 1, url_item)

            # Preview button
            preview_btn = QtWidgets.QPushButton("Preview")
            preview_btn.setMaximumSize(60, 26)
            preview_btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border-radius: 3px;
                    font-size: 10px;
                    padding: 2px 4px;
                }
                QPushButton:hover { background-color: #7f8c8d; }
            """)
            preview_btn.clicked.connect(lambda checked, u=url: self._preview_url(u))
            self.table.setCellWidget(row, 2, preview_btn)

        # Set compact row heights
        for i in range(self.table.rowCount()):
            self.table.setRowHeight(i, 30)

    def _on_selection_changed(self):
        """Handle table selection changes to enable/disable buttons."""
        has_selection = len(self.table.selectedItems()) > 0
        selected_row = self.table.currentRow()
        is_valid_row = 0 <= selected_row < len(self.links_data)

        self.copy_btn.setEnabled(has_selection and is_valid_row)
        self.edit_btn.setEnabled(has_selection and is_valid_row)
        self.delete_btn.setEnabled(has_selection and is_valid_row)

    def _on_item_double_clicked(self, item):
        """Handle double-click on table items to open URLs."""
        if item is None:
            return

        row = item.row()
        if 0 <= row < len(self.links_data):
            link = self.links_data[row]
            url = link.get('url', '')
            name = link.get('name', '')
            if url:
                self._open_link(url, name)

    def _copy_selected_url(self):
        """Copy URL of selected row."""
        row = self.table.currentRow()
        if 0 <= row < len(self.links_data):
            url = self.links_data[row].get('url', '')
            if url:
                self._copy_url(url)

    def _edit_selected_link(self):
        """Edit selected link."""
        row = self.table.currentRow()
        if 0 <= row < len(self.links_data):
            self._edit_link(row)

    def _delete_selected_link(self):
        """Delete selected link."""
        row = self.table.currentRow()
        if 0 <= row < len(self.links_data):
            self._delete_link(row)

    def _add_link(self):
        """Add a new link."""
        dialog = LinkEditDialog(self)
        if dialog.exec_() == QtWidgets.QDialog.DialogCode.Accepted:
            link_data = dialog.get_link_data()
            self.links_data.append(link_data)
            self._save_links()
            self._refresh_table()
            
    def _edit_link(self, row: int):
        """Edit an existing link."""
        if 0 <= row < len(self.links_data):
            dialog = LinkEditDialog(self, self.links_data[row])
            if dialog.exec_() == QtWidgets.QDialog.DialogCode.Accepted:
                self.links_data[row] = dialog.get_link_data()
                self._save_links()
                self._refresh_table()
                
    def _delete_link(self, row: int):
        """Delete a link after confirmation."""
        if 0 <= row < len(self.links_data):
            link_name = self.links_data[row].get('name', 'Unknown')
            reply = QtWidgets.QMessageBox.question(
                self, 
                "Confirm Delete", 
                f"Are you sure you want to delete the link '{link_name}'?",
                QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No
            )
            if reply == QtWidgets.QMessageBox.StandardButton.Yes:
                del self.links_data[row]
                self._save_links()
                self._refresh_table()
                
    def _open_link(self, url: str, name: str):
        """Open a link in the browser."""
        if url:
            open_in_edge(url)
            # Log the action
            append_log(self.log_path, action="open_link", group="Links", detail=name, extra={"url": url})
            
    def _copy_url(self, url: str):
        """Copy URL to clipboard."""
        if url:
            clipboard = QtWidgets.QApplication.clipboard()
            clipboard.setText(url)
            # Show temporary status message
            QtWidgets.QMessageBox.information(self, "Copied", f"URL copied to clipboard:\n{url}")
            
    def _preview_url(self, url: str):
        """Show full URL in a preview dialog."""
        if url:
            dialog = QtWidgets.QDialog(self)
            dialog.setWindowTitle("URL Preview")
            dialog.resize(600, 150)
            
            layout = QtWidgets.QVBoxLayout(dialog)
            
            # URL display
            url_edit = QtWidgets.QTextEdit()
            url_edit.setPlainText(url)
            url_edit.setReadOnly(True)
            url_edit.setMaximumHeight(80)
            layout.addWidget(QtWidgets.QLabel("Full URL:"))
            layout.addWidget(url_edit)
            
            # Buttons
            button_layout = QtWidgets.QHBoxLayout()
            
            copy_btn = QtWidgets.QPushButton("Copy")
            copy_btn.clicked.connect(lambda: self._copy_url(url))
            
            open_btn = QtWidgets.QPushButton("Open")
            open_btn.clicked.connect(lambda: open_in_edge(url))
            
            close_btn = QtWidgets.QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            
            button_layout.addStretch()
            button_layout.addWidget(copy_btn)
            button_layout.addWidget(open_btn)
            button_layout.addWidget(close_btn)
            
            layout.addLayout(button_layout)
            dialog.exec_()
            
    def _save_links(self):
        """Save current links data to file."""
        from app.common.utils import DEFAULT_CONFIG_PATH
        if not save_links_data(DEFAULT_CONFIG_PATH, self.links_data):
            QtWidgets.QMessageBox.warning(self, "Save Error", "Failed to save links data.")
